{"name": "chatadvisorserver", "version": "1.0.1", "main": "index.js", "license": "MIT", "engines": {"node": ">=20.0.0"}, "scripts": {"dev": "NODE_ENV=debug PORT=33001 nodemon", "build": "tsc", "start": "node dist/app.js", "release": "npm run build && NODE_ENV=production PORT=53001 ts-node -r tsconfig-paths/register dist/src/index.js", "test": "ts-node ./test/test.ts", "test:new": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "backup": "node scripts/backup.js", "restore": "node scripts/restore.js", "verify-config": "node scripts/verify-xai-config.js", "debug-config": "node scripts/debug-config.js", "deploy": "./scripts/quick-deploy.sh", "config:template": "./scripts/deploy-config.sh --template", "config:validate": "./scripts/deploy-config.sh --validate", "config:backup": "./scripts/deploy-config.sh --backup", "migrate": "ts-node src/scripts/migrate.ts", "migrate:create": "ts-node src/scripts/create-migration.ts", "migrate:rollback": "ts-node src/scripts/rollback-migration.ts", "migrate:version-control": "ts-node src/scripts/migrate-version-control.ts", "db:reset": "ts-node src/scripts/reset-database.ts", "admin:create": "ts-node src/scripts/create-admin.ts", "ai-config:check": "node scripts/check-ai-config-env.js", "pm-release": "npm run build && pm2 start pm2.config.js --only chat-advisor-release", "pm-dev": "npm run build && pm2 start pm2.config.js --only chat-advisor-debug", "pm-all": "npm run build && pm2 start pm2.config.js", "pm-admin": "./scripts/pm2-admin.sh", "pm-admin:start-release": "./scripts/pm2-admin.sh start-release", "pm-admin:start-debug": "./scripts/pm2-admin.sh start-debug", "pm-admin:start-all": "./scripts/pm2-admin.sh start-all", "pm-admin:stop": "./scripts/pm2-admin.sh stop-all", "pm-admin:status": "./scripts/pm2-admin.sh status", "stop": "pm2 stop all", "restart": "pm2 restart all", "delete": "pm2 delete all", "logs": "pm2 logs"}, "dependencies": {"@types/cors": "^2.8.19", "@types/mime-types": "^2.1.4", "@types/oauth": "^0.9.5", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.19.2", "express-handlebars": "^7.1.2", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "google-auth-library": "^9.11.0", "helmet": "^7.0.0", "https-proxy-agent": "^7.0.6", "i18next": "^23.10.1", "i18next-fs-backend": "^2.3.1", "i18next-http-middleware": "^3.5.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "luxon": "^3.4.4", "mime-types": "^2.1.35", "moment-timezone": "^0.5.45", "mongoose": "^8.3.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "next": "^13.5.5", "nodemailer": "^6.9.13", "oauth": "^0.10.0", "openai": "^5.11.0", "query-string": "^9.1.0", "tiktoken": "^1.0.15", "tsconfig-paths": "^4.2.0", "winston": "^3.13.0", "winston-mongodb": "^5.1.1", "zod-to-json-schema": "^3.21.4"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^3.0.0", "@types/body-parser": "^1.19.5", "@types/express": "^4.17.21", "@types/i18next": "^13.0.0", "@types/i18next-fs-backend": "^1.1.5", "@types/jsonwebtoken": "^9.0.6", "@types/luxon": "^3.4.2", "@types/moment-timezone": "^0.5.30", "@types/mongoose": "^5.11.97", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.15", "@types/winston": "^2.4.4", "axios": "^1.6.8", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "prettier": {"printWidth": 200, "eslintIntegration": true, "stylelintIntegration": true, "tabWidth": 4, "semi": true, "singleQuote": true, "bracketSpacing": true, "arrowParens": "avoid", "htmlWhitespaceSensitivity": "ignore", "javascript.format.insertSpaceBeforeFunctionParenthesis": true, "files.insertFinalNewline": true, "useTabs": false, "endOfLine": "auto", "ignorePath": ".gnore", "trailingComma": "none"}}