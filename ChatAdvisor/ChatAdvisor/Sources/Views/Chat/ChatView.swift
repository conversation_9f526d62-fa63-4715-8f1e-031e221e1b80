import Foundation
import Localize_Swift
import SimpleToast
import SwiftUI

struct ChatView: View {
    @Binding var showSideMenu: Bool
    @ObservedObject var bootManager = BootManager.shared
    @EnvironmentObject var viewModel: ChatViewModel
    @EnvironmentObject var contentViewModel: ContentViewModel

    var body: some View {
        VStack {
            // 新增：网络状态指示器
            NetworkStatusIndicator()

            ZStack(alignment: .center) {
                // 聊天详情加载指示器和错误显示（只在初始加载时显示）
                if contentViewModel.isLoadingChatDetails || viewModel.isLoadingInitialMessages {
                    VStack {
                        Spacer()
                        VStack(spacing: 16) {
                            ProgressView()
                                .scaleEffect(1.2)
                                .tint(.accentColor)
                            Text("正在加载聊天记录...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(20)
                        .background(Color(.systemBackground).opacity(0.95))
                        .cornerRadius(16)
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                        Spacer()
                    }
                    .transition(.opacity.combined(with: .scale))
                    .zIndex(1)
                }

                // 错误状态显示
                if let errorMessage = contentViewModel.chatLoadingError {
                    VStack {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .font(.largeTitle)
                                .foregroundColor(.orange)
                            Text(errorMessage)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                            Button("重试") {
                                contentViewModel.chatLoadingError = nil
                            }
                            .buttonStyle(.borderedProminent)
                            .tint(.accentColor)
                        }
                        .padding(20)
                        .background(Color(.systemBackground).opacity(0.95))
                        .cornerRadius(16)
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                        Spacer()
                    }
                    .transition(.opacity.combined(with: .scale))
                    .zIndex(1)
                }
                ScrollViewReader { proxy in
                    ScrollView {
                        scrollContentView
                        .onChange(of: viewModel.currentChat.messages.count) { newCount in
                            handleMessageCountChange(newCount: newCount, proxy: proxy)
                        }
                        .onChange(of: viewModel.currentChat.messages.last?.content) { newContent in
                            handleLastMessageContentChange(newContent: newContent, proxy: proxy)
                        }
                    }
                    .coordinateSpace(name: "scroll")
                    .animation(nil, value: viewModel.currentChat.messages.count)
                    .sheet(item: Binding(
                        get: { viewModel.selectedMessage },
                        set: { viewModel.selectMessage($0) }
                    )) { message in
                        TextSelectionView(message: Binding(
                            get: { viewModel.selectedMessage },
                            set: { viewModel.selectMessage($0) }
                        ))
                    }
                }
            }


            HStack {
                InputBottomView()
                    .environmentObject(viewModel)
            }
            .padding(.horizontal, AppThemes.padding / 2)
        }
        .simpleToast(isPresented: Binding(
            get: { viewModel.showToast },
            set: { _ in /* Toast会自动隐藏 */ }
        ), options: AppThemes.toastOptions) {
            toastLabel
        }
        // {{ AURA-X: Modify - 优化背景色，使用微信风格的聊天背景 }}
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(.systemGray6).opacity(0.3),
                    Color(.systemBackground)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .coordinateSpace(name: "frameLayer")
        .onAppear {
            handleOnAppear()
        }
        .onChange(of: contentViewModel.selectedChatID) { newChatID in
            // 当选中的会话ID改变时，确保消息内容正确加载
            if let newChatID = newChatID {
                // 如果当前ChatView显示的会话与选中的会话ID匹配，但消息为空，则加载消息
                if viewModel.currentChat.id == newChatID &&
                   viewModel.currentChat.messages.isEmpty &&
                   !viewModel.isLoadingInitialMessages &&
                   !viewModel.isLoadingMessage {
                    Task {
                        await viewModel.fetchCurrentChatMessages()
                    }
                }
                // 注意：ChatView的environmentObject会根据ContentView的逻辑自动更新
                // 所以会话ID不匹配是正常的过渡状态，不需要警告
            }
        }
    }

    // MARK: - 视图组件

    private var scrollContentView: some View {
        // {{ AURA-X: Modify - 使用智能间距的消息列表布局 }}
        LazyVStack(spacing: 0) { // 使用0间距，由组件内部控制
            emptyStateView
            smartSpacingMessageListView

            // {{ AURA-X: Remove - 移除独立的AI状态气泡，状态已集成到消息中 }}

            ScrollPositionReader(
                onPositionChange: { isNearBottom in
                    viewModel.scrollManager.updateUserPosition(isNearBottom: isNearBottom)
                },
                onDetailedPositionChange: { position in
                    viewModel.scrollManager.updateScrollPosition(position)

                    if viewModel.scrollManager.shouldTriggerPreload {
                        viewModel.loadMoreIfNeeds()
                    }
                }
            )

            Color.clear
                .frame(height: 16) // 减少底部间距
                .id("bottom")
        }
        .padding(.horizontal, AppThemes.Chat.messageHorizontalPadding)
    }

    private var emptyStateView: some View {
        Group {
            if viewModel.currentChat.messages.filter({ $0.role != .system }).isEmpty && !viewModel.isLoadingInitialMessages {
                // {{ AURA-X: Modify - 优化空状态视图，使用微信风格设计 }}
                VStack(spacing: 24) {
                    Spacer()

                    // 聊天图标
                    ZStack {
                        Circle()
                            .fill(AppThemes.Chat.aiBubbleColor)
                            .frame(width: 80, height: 80)
                            .shadow(
                                color: .black.opacity(0.05),
                                radius: 8,
                                x: 0, y: 2
                            )

                        Image(systemName: "message.circle.fill")
                            .font(.system(size: 40))
                            .foregroundColor(AppThemes.Chat.userBubbleColor)
                    }

                    VStack(spacing: 12) {
                        Text("开始新的对话")
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(.primary)

                        Text("在下方输入框中输入消息开始聊天")
                            .font(.system(size: 16))
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(2)
                    }

                    Spacer()
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, 32)
            }
        }
    }

    // {{ AURA-X: Add - 智能间距的消息列表，根据角色变化调整间距 }}
    private var smartSpacingMessageListView: some View {
        let messages = viewModel.currentChat.messages.filter { $0.role != .system }

        return ForEach(Array(messages.enumerated()), id: \.element.id) { index, message in
            let previousMessage = index > 0 ? messages[index - 1] : nil
            let isRoleChanged = previousMessage?.role != message.role
            let topSpacing = index == 0 ? 0 : (isRoleChanged ? AppThemes.Chat.messageGroupSpacing : AppThemes.Chat.messageVerticalSpacing)

            // {{ AURA-X: Debug - 调试间距应用情况 }}
            // print("Message \(index): role=\(message.role), isRoleChanged=\(isRoleChanged), topSpacing=\(topSpacing)")

            WeChatStyleMessageBubble(message: message)
                .environmentObject(viewModel)
                .contextMenu {
                    weChatStyleContextMenu(for: message)
                }
                .id(message.id)
                .padding(.top, topSpacing)
                .transition(.asymmetric(
                    insertion: .scale(scale: AppThemes.Chat.bubbleScaleEffect)
                        .combined(with: .opacity)
                        .animation(.easeOut(duration: AppThemes.Chat.messageAppearDuration)),
                    removal: .opacity
                ))
                .onAppear {
                    if message == messages.first {
                        viewModel.loadMoreIfNeeds()
                    }
                }
        }
    }

    // 保留原有的消息列表视图作为备用
    private var messageListView: some View {
        ForEach(viewModel.currentChat.messages.filter { $0.role != .system }, id: \.id) { message in
            WeChatStyleMessageBubble(message: message)
                .environmentObject(viewModel)
                .contextMenu {
                    weChatStyleContextMenu(for: message)
                }
                .id(message.id)
                .transition(.asymmetric(
                    insertion: .scale(scale: AppThemes.Chat.bubbleScaleEffect)
                        .combined(with: .opacity)
                        .animation(.easeOut(duration: AppThemes.Chat.messageAppearDuration)),
                    removal: .opacity
                ))
                .onAppear {
                    if message == viewModel.currentChat.messages.filter({ $0.role != .system }).first {
                        viewModel.loadMoreIfNeeds()
                    }
                }
        }
    }

    // {{ AURA-X: Modify - 创建微信风格的上下文菜单，优化图标和布局 }}
    @ViewBuilder
    private func weChatStyleContextMenu(for message: ChatMessage) -> some View {
        Button(action: {
            UIPasteboard.general.string = message.content
            // 添加触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            Label("复制".localized(), systemImage: "doc.on.doc.fill")
                .font(.system(size: 14, weight: .medium))
        }

        Button(action: {
            viewModel.readAloud(text: message.content)
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            Label("朗读".localized(), systemImage: "speaker.wave.3.fill")
                .font(.system(size: 14, weight: .medium))
        }

        Button(action: {
            viewModel.selectMessage(message)
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            Label("选择文本".localized(), systemImage: "text.cursor")
                .font(.system(size: 14, weight: .medium))
        }

        // 如果是AI消息，添加重新生成选项
        if message.role == .assistant {
            Divider()
            Button(action: {
                // TODO: 实现重新生成功能
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
            }) {
                Label("重新生成", systemImage: "arrow.clockwise")
                    .font(.system(size: 14, weight: .medium))
            }
        }
    }

    private var toastLabel: some View {
        // {{ AURA-X: Modify - 优化Toast样式，使用微信风格设计 }}
        HStack(spacing: 8) {
            Image(systemName: viewModel.isRequestError ? "exclamationmark.triangle.fill" : "checkmark.circle.fill")
                .font(.system(size: 16, weight: .medium))

            Text(viewModel.toastMessage)
                .font(.system(size: 14, weight: .medium))
                .lineLimit(2)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: AppThemes.Chat.bubbleCornerRadius)
                .fill(viewModel.isRequestError ? Color.red.opacity(0.9) : AppThemes.Chat.userBubbleColor)
                .shadow(
                    color: .black.opacity(0.15),
                    radius: 8,
                    x: 0, y: 4
                )
        )
        .foregroundColor(.white)
        .padding(.top, 120)
    }

    private var helpLabel: some View {
        Label("chat_help".localized(), systemImage: "questionmark.circle")
            .padding()
            .background(viewModel.isRequestError ? Color.red.opacity(0.8) : Color.mainDark)
            .foregroundColor(.white)
            .cornerRadius(10)
            .padding(.top, 120)
    }

    // MARK: - 私有方法

    private func handleMessageCountChange(newCount: Int, proxy: ScrollViewProxy) {
        viewModel.handleMessageCountChange(newCount: newCount) {
            if viewModel.scrollManager.shouldAutoScroll {
                withAnimation(.easeOut(duration: 0.3)) {
                    proxy.scrollTo("bottom", anchor: .bottom)
                }
                viewModel.scrollManager.clearNewMessages()
            }
        }
    }

    private func handleLastMessageContentChange(newContent: String?, proxy: ScrollViewProxy) {
        viewModel.handleLastMessageContentChange(newContent: newContent) {
            if viewModel.scrollManager.shouldAutoScroll {
                withAnimation(.easeOut(duration: 0.2)) {
                    proxy.scrollTo("bottom", anchor: .bottom)
                }
            }
        }
    }

    private func handleOnAppear() {
        if ChatViewModel.allModels.count == 0 {
            viewModel.getPricing()
        } else {
            viewModel.currentModel = ChatViewModel.allModels.first ?? ChatsModel.default
        }
        Task {
            await viewModel.loadOlderMessages()
        }
    }

    private func scrollToBottomSmoothly(with proxy: ScrollViewProxy) {
        withAnimation(.easeInOut(duration: 0.25)) {
            proxy.scrollTo("bottom", anchor: .bottom)
        }
    }
}

// MARK: - 微信风格UI组件

// {{ AURA-X: Add - 微信风格消息气泡组件 }}
/// 微信风格消息气泡
struct WeChatStyleMessageBubble: View {
    let message: ChatMessage
    @Environment(\.colorScheme) private var colorScheme
    @EnvironmentObject var chatViewModel: ChatViewModel
    @State private var isPressed = false
    @State private var animatingDots: [Bool] = [false, false, false]

    var body: some View {
        // {{ AURA-X: Modify - 减少HStack spacing，进一步压缩消息气泡占用空间 }}
        HStack(alignment: .top, spacing: 0) {
            if message.role == .user {
                Spacer(minLength: UIScreen.main.bounds.width * (1 - AppThemes.Chat.messageBubbleMaxWidth))
                messageContent
            } else {
                messageContent
                Spacer(minLength: UIScreen.main.bounds.width * (1 - AppThemes.Chat.messageBubbleMaxWidth))
            }
        }
    }

    @ViewBuilder
    private var messageContent: some View {
        // {{ AURA-X: Modify - 重新设计消息内容，集成AI状态到消息气泡中 }}
        VStack(alignment: message.role == .user ? .trailing : .leading, spacing: 0) {
            // 消息气泡（包含AI状态）
            messageBubbleWithStatus
        }
    }

    @ViewBuilder
    private var messageBubbleWithStatus: some View {
        // {{ AURA-X: Modify - 集成AI状态到消息气泡中，避免单独的状态气泡 }}
        VStack(alignment: message.role == .user ? .trailing : .leading, spacing: 0) {
            // 主要消息内容
            if shouldShowContent {
                Text(displayContent)
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(message.role == .user ? AppThemes.Chat.userTextColor : AppThemes.Chat.aiTextColor)
                    .padding(AppThemes.Chat.bubbleInnerPadding)
                    .background(bubbleBackground)
                    .fixedSize(horizontal: false, vertical: true)
            }

            // AI状态指示器（当没有内容或正在输入时显示）
            if shouldShowAIStatus {
                aiStatusInBubble
            }
        }
    }

    @ViewBuilder
    private var bubbleBackground: some View {
        RoundedRectangle(cornerRadius: AppThemes.Chat.bubbleCornerRadius)
            .fill(message.role == .user ? AppThemes.Chat.userBubbleColor : AppThemes.Chat.aiBubbleColor)
            .overlay(
                // AI消息添加边框
                message.role == .assistant ?
                RoundedRectangle(cornerRadius: AppThemes.Chat.bubbleCornerRadius)
                    .stroke(AppThemes.Chat.aiBubbleBorderColor, lineWidth: 0.5) : nil
            )
            .shadow(
                color: .black.opacity(AppThemes.Chat.bubbleShadowOpacity),
                radius: AppThemes.Chat.bubbleShadowRadius,
                x: 0, y: 1
            )
    }

    @ViewBuilder
    private var aiStatusInBubble: some View {
        HStack(spacing: 6) {
            // 状态文本
            Text(chatViewModel.aiTypingState.displayText)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(AppThemes.Chat.aiTextColor.opacity(0.7))

            // 动画指示器
            if chatViewModel.aiTypingState == .connecting {
                ProgressView()
                    .scaleEffect(0.7)
                    .tint(AppThemes.Chat.aiTextColor)
            } else if chatViewModel.aiTypingState == .thinking || chatViewModel.aiTypingState == .typing {
                HStack(spacing: 3) {
                    ForEach(0..<3, id: \.self) { index in
                        Circle()
                            .fill(AppThemes.Chat.aiTextColor.opacity(0.6))
                            .frame(width: 5, height: 5)
                            .scaleEffect(animatingDots[index] ? 1.3 : 1.0)
                            .animation(
                                Animation.easeInOut(duration: 0.6)
                                    .repeatForever()
                                    .delay(Double(index) * 0.2),
                                value: animatingDots[index]
                            )
                    }
                }
            }
        }
        .padding(AppThemes.Chat.bubbleInnerPadding)
        .background(bubbleBackground)
        .onAppear {
            startDotAnimation()
        }
    }

    // {{ AURA-X: Add - 显示控制逻辑 }}
    /// 是否应该显示消息内容
    private var shouldShowContent: Bool {
        // 用户消息总是显示内容
        if message.role == .user {
            return true
        }

        // {{ AURA-X: Fix - 优化内容显示逻辑，与状态显示逻辑保持一致 }}
        // AI消息：如果有内容，或者不是当前流式消息，或者AI不在活跃状态时显示
        let isCurrentStreamingMessage = chatViewModel.currentStreamingMessageId == message.id

        // 有内容时总是显示
        if !message.content.isEmpty {
            return true
        }

        // 无内容时：如果不是当前流式消息或AI不活跃，则显示（避免空白）
        return !isCurrentStreamingMessage || chatViewModel.aiTypingState == .idle
    }

    /// 是否应该显示AI状态指示器
    private var shouldShowAIStatus: Bool {
        // {{ AURA-X: Fix - 修复AI状态异常占用问题，只在当前流式消息上显示状态 }}
        // 只有AI消息且AI在活跃状态且是当前正在处理的消息时显示状态
        guard message.role == .assistant else { return false }
        guard chatViewModel.aiTypingState != .idle else { return false }

        // 检查是否是当前正在流式输出的消息
        let isCurrentStreamingMessage = chatViewModel.currentStreamingMessageId == message.id

        // 只有当前流式消息且内容为空时才显示状态
        return isCurrentStreamingMessage && message.content.isEmpty
    }

    /// 获取要显示的内容（支持打字机效果）
    private var displayContent: String {
        if message.role == .assistant && !message.isDisplayComplete {
            let streamingContent = chatViewModel.getStreamingMessageDisplayContent(for: message.id)
            return streamingContent.isEmpty ? message.content : streamingContent
        }
        return message.content
    }

    /// 启动点点动画
    private func startDotAnimation() {
        guard chatViewModel.aiTypingState == .thinking || chatViewModel.aiTypingState == .typing else { return }

        for i in 0..<3 {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(i) * 0.2) {
                withAnimation(.easeInOut(duration: 0.6).repeatForever()) {
                    animatingDots[i] = true
                }
            }
        }
    }
}

// {{ AURA-X: Remove - 移除独立的打字指示器，已集成到消息气泡中 }}


